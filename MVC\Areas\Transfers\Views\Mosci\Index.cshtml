@model Odrc.Dots.Areas.Transfers.Models.Mosci.MosciPageViewModel

@{
    ViewBag.ScreenDescription = "Schedule Inmate Move  -  IMOSC";
    Layout = "~/Areas/Home/Views/Shared/_Layout.cshtml";
}


@if (!string.IsNullOrWhiteSpace(ViewBag.Message))
{
    <div class="alert alert-success fade in">
        <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
        @ViewBag.Message
    </div>
}
@if (!string.IsNullOrWhiteSpace(ViewBag.ErrorMessage))
{
    <div class="alert alert-danger fade in">
        <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
        @ViewBag.ErrorMessage
    </div>
}

@if (!string.IsNullOrWhiteSpace(Model.Message))
{
    <div class="alert alert-success fade in">
        <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
        @Model.Message
    </div>
}
@if (!string.IsNullOrWhiteSpace(Model.ErrorMessage))
{
    <div class="alert alert-danger fade in">
        <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
        @Model.ErrorMessage
    </div>
}


<div id="divErrorMessage" class="alert alert-danger fade in" style="display:none">
    <a href="#" class="close" data-dismiss="alert" aria-label="close">X</a>
    <span id="ErrorMessage"></span>
</div>




@* Hidden fields for search functionality *@
@Html.HiddenFor(m => m.SearchPrefix)
@Html.HiddenFor(m => m.SearchOffenderId)




@using (Html.BeginForm("Mosci", "Mosci", new { area = "Transfers" }, FormMethod.Post, new { @id = "Mosci", @class = "form-horizontal" }))
{
    @Html.AntiForgeryToken()

    @* Hidden field for JSON model data (used by JavaScript) *@
    @Html.Hidden("modelJson", "", new { id = "modelJson" })

    @* Hidden fields for auto-population functionality *@
    @Html.Hidden("autoPopulateRowIndex", "", new { id = "autoPopulateRowIndex" })

    <div id="Housing-Manage" class="no-print">
        <div class="row">
            <div class="col-md-12">
                <div class="divFindOffender">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Find Inmate
                        </div>
                        <div class="panel-body">
                            <div class="form-inline">
                                <div class="form-group col-xs-12 col-sm-6 col-md-6">
                                    @Html.DropDownListFor(m => m.SearchPrefix, Model.PrefixOptions, new { @class = "form-control input-sm", @id = "searchPrefixDropdown" })
                                    @Html.TextBoxFor(m => m.SearchOffenderId, new { @class = "form-control input-sm onlyNumeric", @autofocus = "autofocus", @id = "txtInmateNum", maxlength = "6" })
                                    
                                    <button id="btnFindOffender" type="submit" class="btn btn-primary" name="submitAction" value="Search">
                                        <span>Find Inmate</span>
                                    </button>
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    
    <div class="panel panel-primary">
        <div class="panel-heading">
            Schedule Inmate Move&nbsp;&nbsp;-&nbsp;&nbsp;MOSCI
        </div>
        <div class="panel-body">
            <div class="table-responsive">
                <table id="inmateTable" class="table table-bordered table-condensed">
                    <thead class="odrc-header-row">
                        <tr>
                            <td style="width:70px;">Prefix</td>
                            <td style="width:120px;">Offender #</td>
                            <td style="width:150px;">Last Name</td>
                            <td style="width:150px;">First Name</td>
                            <td style="width:120px;">From</td>
                            <td style="width:140px;">To</td>
                            <td style="width:140px;">Scheduled Date</td>
                            <td style="width:120px;">Comments</td>
                            <td style="width:50px;">Remove</td>
                            <td style="width:50px;">Delete</td>
                        </tr>
                    </thead>
                    <tbody>
                        @for (int i = 0; i < Model.Inmates.Count; i++)
                        {
                            <tr @if (i == 0) { <text> id="inmate-row-template" </text> }>
                                <td>
                                    @Html.DropDownListFor(m => m.Inmates[i].InmateIdPrefix, Model.PrefixOptions, new { @class = "form-control input-sm" })
                                    @Html.HiddenFor(m => m.Inmates[i].Recno)
                                    @Html.HiddenFor(m => m.Inmates[i].OffenderId)
                                </td>
                                <td>@Html.TextBoxFor(m => m.Inmates[i].OffenderId, new { @class = "form-control input-sm onlyNumeric auto-populate-field", @maxlength = "6" })</td>
                                <td>@Html.TextBoxFor(m => m.Inmates[i].LastName, new { @class = "form-control input-sm", @readonly = "readonly" })</td>
                                <td>@Html.TextBoxFor(m => m.Inmates[i].FirstName, new { @class = "form-control input-sm", @readonly = "readonly" })</td>
                                <td>
                                    
                                    @{

                                        var frominsText = "";
                                        if (Model.Inmates[i].Instno.HasValue)
                                        {
                                            var fromOption = Model.InstnoDrp.FirstOrDefault(x => x.Value == Model.Inmates[i].Instno.ToString());
                                            frominsText = fromOption?.Text ?? "";
                                        }
                                        else if (Model.Inmates[i].Instname != null)
                                        {
                                            frominsText = Model.Inmates[i].Instname;
                                        }
                                    }

                                    @Html.TextBoxFor(m => m.Inmates[i].Instno, new { @class = "form-control input-sm", @readonly = "readonly", @style = "display:none;" })
                                    @Html.TextBox("frominsText", frominsText, new { @class = "form-control input-sm", @readonly = "readonly" })
                                </td>
                                
                                <td>
                                    @{
                                        var item = Model.SchdInstDrp
                                            .Select(x => new SelectListItem
                                            {
                                                Text = x.Text,
                                                Value = x.Value,
                                                Selected = (x.Value == Model.Inmates[i].SchdInst.ToString())

                                            });
                                    }
                                    @Html.DropDownListFor(m => m.Inmates[i].SchdInst, item, new { @class = "form-control input-sm" })

                                    

                                </td>

                                <td>
                                    <div class="input-group input-group-sm">
                                        @Html.TextBoxFor(m => m.Inmates[i].SchDate,
                                       new
                                            {
                                           @class = "form-control input-sm datepicker-input",
                                           @Value = (Model.Inmates[i].SchDate == DateTime.MinValue ? "" : Model.Inmates[i].SchDate.ToString("MM/dd/yyyy")),
                                           PlaceHolder = "MM/dd/yyyy",
                                           autocomplete = "off"
                                       })
                                        <span class="input-group-addon datepicker-trigger" style="cursor: pointer;">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </td>
                                <td>@Html.TextAreaFor(m => m.Inmates[i].Descrl, new { @class = "form-control input-sm comment-textarea", @rows = "2", @style = "resize: vertical; min-height: 34px;" })</td>
                                <td class="text-center">@Html.CheckBoxFor(m => m.Inmates[i].IsMarkedForRemoval)</td>
                                <td class="text-center">@Html.CheckBoxFor(m => m.Inmates[i].IsMarkedForDeletion)</td>
                            </tr>
                        }
                    </tbody>
                </table>

                <!-- Hidden template row for when no rows exist -->
                <table style="display: none;">
                    <tbody>
                        <tr id="empty-row-template">
                            <td>
                                @Html.DropDownList("TemplateInmateIdPrefix", Model.PrefixOptions, new { @class = "form-control input-sm", @id = "TemplateInmateIdPrefix" })
                                <input name="TemplateRecno" id="TemplateRecno" type="hidden" value="0" />
                                <input name="TemplateOffenderId" id="TemplateOffenderId_Hidden" type="hidden" value="" />
                            </td>
                            <td><input name="TemplateOffenderId" id="TemplateOffenderId" class="form-control input-sm onlyNumeric auto-populate-field" maxlength="6" type="text" value="" data-row-index="0" /></td>
                            <td><input name="TemplateLastName" id="TemplateLastName" class="form-control input-sm" readonly="readonly" type="text" value="" /></td>
                            <td><input name="TemplateFirstName" id="TemplateFirstName" class="form-control input-sm" readonly="readonly" type="text" value="" /></td>
                            <td>
                                <input name="TemplateInstno" id="TemplateInstno" class="form-control input-sm" readonly="readonly" style="display:none;" type="text" value="" />
                                <input name="TemplatefrominsText" id="TemplatefrominsText" class="form-control input-sm" readonly="readonly" type="text" value="" />
                            </td>
                            <td>
                                @Html.DropDownList("TemplateSchdInst", Model.SchdInstDrp, new { @class = "form-control input-sm", @id = "TemplateSchdInst" })
                            </td>
                            <td>
                                <div class="input-group input-group-sm">
                                    <input name="TemplateSchDate" id="TemplateSchDate" class="form-control input-sm datepicker-input" placeholder="MM/dd/yyyy" autocomplete="off" type="text" value="" />
                                    <span class="input-group-addon datepicker-trigger" style="cursor: pointer;">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </td>
                            <td><textarea name="TemplateDescrl" id="TemplateDescrl" class="form-control input-sm comment-textarea" rows="2" style="resize: vertical; min-height: 34px;"></textarea></td>
                            <td class="text-center">
                                <input name="TemplateIsMarkedForRemoval" id="TemplateIsMarkedForRemoval" type="checkbox" value="true" />
                                <input name="TemplateIsMarkedForRemoval" type="hidden" value="false" />
                            </td>
                            <td class="text-center">
                                <input name="TemplateIsMarkedForDeletion" id="TemplateIsMarkedForDeletion" type="checkbox" value="true" />
                                <input name="TemplateIsMarkedForDeletion" type="hidden" value="false" />
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Add / Remove Row Buttons -->
            <div class="row">
                <div class="col-md-6 col-xs-12">
                    
                    <button type="submit" id="btnAddNewInmate" class="btn btn-primary">
                        <span class="glyphicon glyphicon-plus"></span> Add New Inmate
                    </button>
                </div>
                <div class="col-md-6 col-xs-12 text-right">
                    <button type="submit" name="submitAction" value="RemoveSelected" name="btnRemoveInmate" class="btn btn-danger">
                        <span class="glyphicon glyphicon-remove"></span> Remove Inmate
                    </button>
                    <span style="display:inline-block; width:20px;"></span>
                    <button type="submit" name="submitAction" value="DeleteSelected" id="btnDelete" class="btn btn-default">
                        <span class="glyphicon glyphicon-trash"></span> Delete
                    </button>
                </div>
            </div>
            <br />
            <!-- Save / Cancel Buttons -->
            <div class="row text-center">
                <button type="submit" name="submitAction" value="Save" class="btn btn-primary" id="btnSave">
                    <span class="glyphicon glyphicon-floppy-disk"></span> Save
                </button>
                <button type="submit" name="submitAction" value="Cancel" class="btn btn-default" id="btnCancel">
                    <span class="glyphicon glyphicon-remove-circle"></span> Cancel
                </button>
            </div>
        </div>
    </div>

    <!-- Custom Confirmation Modal for Cancel -->
    <div class="modal fade" id="cancelConfirmModal" tabindex="-1" role="dialog" aria-labelledby="cancelConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="cancelConfirmModalLabel">Confirm Cancel</h4>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to cancel? Any unsaved changes will be lost.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">No</button>
                    <button type="button" class="btn btn-primary" id="confirmCancelBtn">Yes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Confirmation Modal for Delete -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" role="dialog" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="deleteConfirmModalLabel">Confirm Delete</h4>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete an inmate from MOSCI?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">No</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Yes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Confirmation Modal for Remove -->
    <div class="modal fade" id="removeConfirmModal" tabindex="-1" role="dialog" aria-labelledby="removeConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="removeConfirmModalLabel">Confirm Remove</h4>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to remove selected inmates?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">No</button>
                    <button type="button" class="btn btn-danger" id="confirmRemoveBtn">Yes</button>
                </div>
            </div>
        </div>
    </div>


    @section PageCss
{
        <link href="~/Areas/Rh/Content/CountOffice/jquery-ui.css" rel="stylesheet" />
        <link href="~/Areas/Rh/Content/CountOffice/jquery-ui.theme.css" rel="stylesheet" />

        <style>
            /* Date picker styling */
            .datepicker-trigger {
                background-color: #f5f5f5;
                border-left: 1px solid #ccc;
                padding: 5px 8px;
            }

                .datepicker-trigger:hover {
                    background-color: #e8e8e8;
                }

                .datepicker-trigger .glyphicon-calendar {
                    color: #337ab7;
                    font-size: 12px;
                }

            /* Ensure input group sizing is consistent */
            .input-group-sm .input-group-addon {
                padding: 5px 8px;
                font-size: 12px;
                line-height: 1.5;
                border-radius: 3px;
            }

            /* Custom datepicker styling to match the expected result */
            .ui-datepicker {
                font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
                font-size: 11px;
                width: 200px;
                border: 1px solid #999;
                background: white;
                box-shadow: 2px 2px 5px rgba(0,0,0,0.3);
                padding: 0;
            }

                .ui-datepicker .ui-datepicker-header {
                    background: #f0f0f0;
                    border: none;
                    border-bottom: 1px solid #ccc;
                    color: #333;
                    font-weight: normal;
                    padding: 4px 8px;
                    position: relative;
                    height: 20px;
                }

                .ui-datepicker .ui-datepicker-prev,
                .ui-datepicker .ui-datepicker-next {
                    position: absolute;
                    top: 2px;
                    width: 16px;
                    height: 16px;
                    background: #f0f0f0;
                    border: 1px solid #999;
                    cursor: pointer;
                    text-align: center;
                    line-height: 14px;
                    font-size: 10px;
                    color: #333;
                }

                .ui-datepicker .ui-datepicker-prev {
                    left: 4px;
                }

                .ui-datepicker .ui-datepicker-next {
                    right: 4px;
                }

                    .ui-datepicker .ui-datepicker-prev:hover,
                    .ui-datepicker .ui-datepicker-next:hover {
                        background: #e0e0e0;
                    }

                .ui-datepicker .ui-datepicker-title {
                    line-height: 20px;
                    margin: 0 25px;
                    text-align: center;
                    font-weight: bold;
                    font-size: 11px;
                }

                /* Style the month and year dropdowns */
                .ui-datepicker .ui-datepicker-month,
                .ui-datepicker .ui-datepicker-year {
                    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
                    font-size: 11px;
                    border: 1px solid #999;
                    background: white;
                    padding: 1px 2px;
                    margin: 0 2px;
                    color: #333;
                }

                .ui-datepicker .ui-datepicker-month {
                    width: 70px;
                }

                .ui-datepicker .ui-datepicker-year {
                    width: 50px;
                }

                .ui-datepicker table {
                    width: 100%;
                    font-size: 11px;
                    border-collapse: collapse;
                    margin: 0;
                    border-spacing: 0;
                }

                .ui-datepicker th {
                    padding: 4px 2px;
                    text-align: center;
                    font-weight: normal;
                    border: none;
                    background: #f8f8f8;
                    color: #666;
                    font-size: 10px;
                    border-bottom: 1px solid #ddd;
                }

                .ui-datepicker td {
                    border: none;
                    padding: 0;
                    text-align: center;
                }

                    .ui-datepicker td span,
                    .ui-datepicker td a {
                        display: block;
                        padding: 2px;
                        text-align: center;
                        text-decoration: none;
                        border: 1px solid transparent;
                        font-size: 11px;
                        color: #333;
                        width: 20px;
                        height: 16px;
                        line-height: 16px;
                        margin: 1px;
                    }

                        .ui-datepicker td a:hover {
                            background: #316AC5;
                            color: white;
                            border: 1px solid #316AC5;
                        }

                .ui-datepicker .ui-datepicker-today a {
                    background: #316AC5;
                    color: white;
                    font-weight: bold;
                    border: 1px solid #316AC5;
                }

                .ui-datepicker .ui-datepicker-current-day a {
                    background: #316AC5;
                    color: white;
                    font-weight: bold;
                    border: 1px solid #316AC5;
                }

                .ui-datepicker .ui-datepicker-other-month a {
                    color: #ccc;
                }

                /* Hide the default jQuery UI icons and use custom arrows */
                .ui-datepicker .ui-icon {
                    display: none;
                }

                .ui-datepicker .ui-datepicker-prev:before {
                    content: "◀";
                    font-size: 8px;
                }

                .ui-datepicker .ui-datepicker-next:before {
                    content: "▶";
                    font-size: 8px;
                }

            /* Comment textarea styling */
            .comment-textarea {
                resize: vertical !important;
                min-height: 34px !important;
                max-height: 35px;
                font-family: inherit;
                font-size: 12px;
                line-height: 1.4;
                padding: 6px 12px;
                border: 1px solid #ccc;
                border-radius: 4px;
                box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
                transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
            }

                .comment-textarea:focus {
                    border-color: #66afe9;
                    outline: 0;
                    box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102,175,233,.6);
                }

            /* Ensure the table cell accommodates the resizable textarea */
            #inmateTable td:nth-child(8) {
                vertical-align: top;
                min-width: 250px;
            }

            /* Custom Modal Styling to match page theme */
            .modal-content {
                border-radius: 4px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.3);
                border: 1px solid #ddd;
            }

            .modal-header {
                background-color: #337ab7;
                color: white;
                border-bottom: 1px solid #337ab7;
                padding: 15px;
                border-radius: 4px 4px 0 0;
            }

                .modal-header .modal-title {
                    font-size: 16px;
                    font-weight: bold;
                    margin: 0;
                }

                .modal-header .close {
                    color: white;
                    opacity: 0.8;
                    font-size: 21px;
                    font-weight: bold;
                    line-height: 1;
                    text-shadow: none;
                }

                    .modal-header .close:hover,
                    .modal-header .close:focus {
                        color: white;
                        opacity: 1;
                        text-decoration: none;
                    }

            .modal-body {
                padding: 20px;
                font-size: 14px;
                line-height: 1.42857143;
                color: #333;
            }

            .modal-footer {
                padding: 15px;
                text-align: right;
                border-top: 1px solid #e5e5e5;
                background-color: #f5f5f5;
                border-radius: 0 0 4px 4px;
            }

                .modal-footer .btn {
                    margin-left: 10px;
                    min-width: 80px;
                }

            .modal-backdrop {
                background-color: #000;
            }

                .modal-backdrop.fade {
                    opacity: 0;
                }

                .modal-backdrop.in {
                    opacity: 0.5;
                }
        </style>

    }


    @section PageScripts {

        <!-- jQuery UI JavaScript for datepicker functionality -->
        <script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>

        <!-- Define MyScript object to prevent errors -->
        <script type="text/javascript">


            // Define MyScript object if it doesn't exist
            var MyScript = MyScript || {};

            // Add init method if it doesn't exist
            MyScript.init = MyScript.init || function(options) {
                console.log("MyScript.init called with options:", options);
                window.m_options = options; // Store options globally if needed
            };

            var MOSCI = MOSCI || {};
            MOSCI.MAX_ROWS = 20;

            // Flag to track if delete button state is controlled by Mant field validation
            var deleteButtonControlledByMant = false;

            // Define updateButtonState function early to prevent "undefined" errors
            var updateButtonState = function () {
                try {
                    console.log('=== updateButtonState called ===');
                    var rowCount = $('#inmateTable tbody tr').length;
                    var isMaxRowsReached = rowCount >= MOSCI.MAX_ROWS;

                    // Check if any schedule date is not minimum value (not empty/default)
                    var hasNonMinScheduleDate = false;
                    var hasUnscheduledInmates = false;

                    $('#inmateTable tbody tr').each(function () {
                        var $row = $(this);
                        var schDateValue = $row.find('input[id*="SchDate"]').val();
                        var offenderId = $row.find('input[id*="OffenderId"]').val();

                        // Check if row has data (offender ID is not empty)
                        if (offenderId && offenderId.trim() !== '') {
                            // Check if the date field has a value and is not empty
                            if (schDateValue && schDateValue.trim() !== '') {
                                // Check for specific minimum date patterns that indicate DateTime.MinValue
                                var isMinValue = schDateValue === '01/01/0001' ||
                                    schDateValue === '1/1/0001' ||
                                    schDateValue === '01/01/1' ||
                                    schDateValue === '1/1/1';

                                // If it's not a minimum value pattern, then it's a real date
                                if (!isMinValue) {
                                    var parsedDate = new Date(schDateValue);
                                    // If it's a valid date and not a minimum value, it's scheduled
                                    if (!isNaN(parsedDate.getTime())) {
                                        hasNonMinScheduleDate = true;
                                    }
                                } else {
                                    // Has minimum value date, so it's unscheduled
                                    hasUnscheduledInmates = true;
                                }
                            } else {
                                // No date value, so it's unscheduled
                                hasUnscheduledInmates = true;
                            }
                        }
                    });

                    // Update Add New Inmate button
                    var shouldDisableAdd = isMaxRowsReached || hasNonMinScheduleDate;
                    $('#btnAddNewInmate').prop('disabled', shouldDisableAdd);

                    // Update button title/tooltip to explain why it's disabled
                    var buttonTitle = '';
                    if (isMaxRowsReached) {
                        buttonTitle = 'Maximum number of rows reached. Cannot add more inmates.';
                    } else if (hasNonMinScheduleDate) {
                        buttonTitle = 'Cannot add new inmates when schedule date is set. Please clear schedule dates first.';
                    } else {
                        buttonTitle = 'Add a new inmate row';
                    }
                    $('#btnAddNewInmate').attr('title', buttonTitle);

                    // Update Delete button based on scheduled/unscheduled inmates
                    // Only update if not controlled by Mant field validation
                    if (!deleteButtonControlledByMant) {
                        var $deleteBtn = $('#btnDelete');
                        if (hasUnscheduledInmates) {
                            // Has unscheduled inmates - make button grey and disabled
                            $deleteBtn.removeClass('btn-danger').addClass('btn-default');
                            $deleteBtn.prop('disabled', true);
                            $deleteBtn.attr('title', 'Cannot delete when there are unscheduled inmates. Please schedule all inmates first.');
                        } else if (hasNonMinScheduleDate) {
                            // Only scheduled inmates - make button red and enabled
                            $deleteBtn.removeClass('btn-default').addClass('btn-danger');
                            $deleteBtn.prop('disabled', false);
                            $deleteBtn.attr('title', 'Delete selected scheduled inmates');
                        } else {
                            // No inmates with data - disable button
                            $deleteBtn.removeClass('btn-danger').addClass('btn-default');
                            $deleteBtn.prop('disabled', true);
                            $deleteBtn.attr('title', 'No inmates to delete');
                        }
                    } else {
                        console.log('Delete button state controlled by Mant field validation - skipping default update');
                    }

                    var reason = '';
                    if (isMaxRowsReached) {
                        reason = 'maximum rows reached';
                    } else if (hasNonMinScheduleDate) {
                        reason = 'schedule date is not minimum value';
                    } else {
                        reason = 'enabled';
                    }

                    console.log('Row count: ' + rowCount + ', Add button ' + (shouldDisableAdd ? 'disabled' : 'enabled') + ' (' + reason + ')');
                    console.log('Delete button state: ' + (hasUnscheduledInmates ? 'disabled (unscheduled inmates)' : hasNonMinScheduleDate ? 'enabled (only scheduled)' : 'disabled (no inmates)'));
                } catch (error) {
                    console.error('Error in updateButtonState:', error);
                    console.error('Stack trace:', error.stack);
                }
            };

            // Function to validate duplicate offenders in the grid
            function validateDuplicateOffender(currentRowIndex, prefix, offenderId) {
                if (!prefix || !offenderId || offenderId.trim() === '') {
                    return { isValid: true, message: '' };
                }

                var combinedId = prefix + offenderId.trim();
                var duplicateFound = false;
                var duplicateRowIndex = -1;

                // Check all other rows for the same prefix + offender ID combination
                $('#inmateTable tbody tr').each(function (index) {
                    if (index !== currentRowIndex) {
                        var $row = $(this);
                        var rowPrefix = $row.find('select[id*="InmateIdPrefix"]').val() || '';
                        var rowOffenderId = $row.find('input[id*="OffenderId"]').val() || '';

                        if (rowPrefix && rowOffenderId.trim() !== '') {
                            var rowCombinedId = rowPrefix + rowOffenderId.trim();
                            if (rowCombinedId === combinedId) {
                                duplicateFound = true;
                                duplicateRowIndex = index + 1; // 1-based for user display
                                return false; // Break out of each loop
                            }
                        }
                    }
                });

                if (duplicateFound) {
                   // alert(`Duplicate offender found! Offender ${combinedId} already exists in row ${duplicateRowIndex}.`);
                    alert(`Inmate has already been added`);
                    return {
                        isValid: false,
                       // message: `Duplicate offender found! Offender ${combinedId} already exists in row ${duplicateRowIndex}.`
                        message: `Inmate has already been added`

                    };
                }

                return { isValid: true, message: '' };
            }

            // Function to show validation error message
            function showValidationError(message) {
                $('#ErrorMessage').text(message);
                $('#divErrorMessage').show();

                // Auto-hide after 10 seconds
                setTimeout(function () {
                    $('#divErrorMessage').hide();
                }, 1000);
            }

            // Function to hide validation error message
            function hideValidationError() {
                $('#divErrorMessage').hide();
            }


            // Auto-populate function for offender data - JavaScript-only implementation
            function autoPopulateOffender(inputElement, rowIndex, inmateIdPrefix) {
                var combinedOffenderId = inputElement.value.trim();
                console.log('=== Auto-populate triggered ===');
                console.log('Row Index:', rowIndex);
                console.log('Combined Offender ID:', combinedOffenderId);

                var $row = $('#inmateTable tbody tr').eq(rowIndex);

                if (!combinedOffenderId) {
                    // Clear fields if offender ID is empty
                    clearOffenderFields(rowIndex);
                    //hideValidationError();
                    return;
                }


                var prefix = inmateIdPrefix || "";

                var offenderId = combinedOffenderId;

                

                // Validate for duplicates before proceeding
                var validation = validateDuplicateOffender(rowIndex, prefix, offenderId);
                if (!validation.isValid) {
                    //showValidationError(validation.message);

                    // Clear the current row's offender ID to prevent duplicate
                    $row.find('input[id*="OffenderId"]').val('');
                    clearOffenderFields(rowIndex);
                    return;
                }

                // Hide any previous validation errors
                //hideValidationError();



                // Update the prefix dropdown and hidden OffenderId field
                if (prefix) {
                    $row.find('select[id*="InmateIdPrefix"]').val(prefix);
                }
                $row.find('input[id*="OffenderId"]').val(offenderId);


                // Make AJAX call to get offender data
                $.ajax({
                    url: '@Url.Action("GetOffenderData", "Mosci", new { area = "Transfers" })',
                    type: 'POST',
                    data: {
                        prefix: prefix,
                        offenderId: offenderId
                    },
                    success: function(result) {
                        console.log('Auto-populate response:', result);

                        if (result.success && result.offender) {
                            var offender = result.offender;
                            //to clear previous hold values
                            $row.find('select[id*="SchdInst"], select[name*="SchdInst"]').val('');
                            $row.find('input[id*="SchDate"], input[name*="SchDate"]').val('');
                            $row.find('textarea[id*="Descrl"], textarea[name*="Descrl"]').val('');

                            // Populate the fields using flexible selectors (ID or name attribute)
                            var $lastNameField = $row.find('input[id*="LastName"], input[name*="LastName"]');
                            var $firstNameField = $row.find('input[id*="FirstName"], input[name*="FirstName"]');
                            var $fromInsField = $row.find('input[id*="frominsText"], input[name*="frominsText"]');
                            var $instnoField = $row.find('input[id*="Instno"], input[name*="Instno"]');

                            $lastNameField.val(offender.lastName || '');
                            $firstNameField.val(offender.firstName || '');

                            // Set From Institution if available
                            if (offender.frominsText) {
                                $fromInsField.val(offender.frominsText || '');

                                // Set the Instno field from server response
                                if (offender.instno) {
                                    $instnoField.val(offender.instno);
                                    console.log('Set Instno to:', offender.instno, 'for institution:', offender.frominsText);
                                } else {
                                    console.warn('No Instno returned for institution:', offender.frominsText);
                                }
                            }

                            console.log('Auto-populated:', offender.lastName, offender.firstName);
                            console.log('Fields found - LastName:', $lastNameField.length, 'FirstName:', $firstNameField.length, 'FromIns:', $fromInsField.length);

                            // After successful auto-population, check delete eligibility
                            checkDeleteEligibility(prefix, offenderId);
                        } else if (result.message === 'Inmate already schedule.' && result.success === 'true') {
                            //$('.alert-success').hide(); // hide success green messages

                            var message = result.message;
                            alert(message);
                            alert('You must complete the process of scheduling before you can proceed to unschedule an inmate');
                            //showValidationError(message);
                            //hideValidationError();
                        }
                        else if (result.message === 'No matching offender found.') {
                            // Clear fields if no match found using flexible selectors
                            $row.find('input[id*="LastName"], input[name*="LastName"]').val('');
                            $row.find('input[id*="FirstName"], input[name*="FirstName"]').val('');
                            $row.find('input[id*="frominsText"], input[name*="frominsText"]').val('');
                            $row.find('input[id*="Instno"], input[name*="Instno"]').val('');
                            //$row.find('select[id*="SchdInst"], select[name*="SchdInst"]').val('');
                            //$row.find('input[id*="SchDate"], input[name*="SchDate"]').val('');
                            //$row.find('textarea[id*="Descrl"], textarea[name*="Descrl"]').val('');

                            alert('Offender does not exist.');
                        } else { }
                    },
                    error: function(xhr, status, error) {
                        console.error('Auto-populate error:', error);
                        // Clear fields on error using flexible selectors
                        $row.find('input[id*="LastName"], input[name*="LastName"]').val('');
                        $row.find('input[id*="FirstName"], input[name*="FirstName"]').val('');
                        $row.find('input[id*="frominsText"], input[name*="frominsText"]').val('');
                        $row.find('input[id*="Instno"], input[name*="Instno"]').val('');
                    }
                });
            }

            // Function to check delete eligibility based on Mant field
            function checkDeleteEligibility(prefix, offenderId) {
                if (!prefix || !offenderId) {
                    console.log('Skipping delete eligibility check - missing prefix or offender ID');
                    return;
                }

                console.log('Checking delete eligibility for:', prefix + offenderId);

                $.ajax({
                    url: '@Url.Action("CheckDeleteEligibility", "Mosci", new { area = "Transfers" })',
                    type: 'POST',
                    data: {
                        prefix: prefix,
                        offenderId: offenderId
                    },
                    success: function(result) {
                        console.log('Delete eligibility response:', result);

                        if (result.success) {
                            updateDeleteButtonState(result.eligible);
                        } else {
                            console.error('Delete eligibility check failed:', result.message);
                            updateDeleteButtonState(false);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Delete eligibility check error:', error);
                        updateDeleteButtonState(false);
                    }
                });
            }

            // Function to update delete button state based on eligibility
            function updateDeleteButtonState(isEligible) {
                var $deleteBtn = $('#btnDelete');

                // Set flag to indicate delete button is controlled by Mant validation
                deleteButtonControlledByMant = true;

                if (isEligible) {
                    // Enable delete button and make it red
                    $deleteBtn.removeClass('btn-default').addClass('btn-danger');
                    $deleteBtn.prop('disabled', false);
                    $deleteBtn.attr('title', 'Delete selected inmates - Mant field is CHG');
                    console.log('Delete button enabled (red) - Mant field is CHG');
                } else {
                    // Disable delete button and make it gray
                    $deleteBtn.removeClass('btn-danger').addClass('btn-default');
                    $deleteBtn.prop('disabled', true);
                    $deleteBtn.attr('title', 'Delete button disabled - Mant field is not CHG');
                    console.log('Delete button disabled (gray) - Mant field is not CHG');
                }
            }



            $(document).on('change', '.auto-populate-field', function () {
                var $this = $(this);
                var rowIndex = $this.data('row-index') || $this.closest('tr').index();
                var $row = $this.closest('tr');
                var inmateIdPrefix = $row.find('select[id*="InmateIdPrefix"], select[name*="InmateIdPrefix"]').val() || '';
                
                console.log('Change event - rowIndex:', rowIndex, 'inmateIdPrefix:', inmateIdPrefix);
                autoPopulateOffender(this, rowIndex, inmateIdPrefix);
            });

            $(document).on('keydown', '.auto-populate-field', function (event) {
                if (event.keyCode == 13 || event.keyCode == 9) { // Enter or Tab
                    var $this = $(this);
                    var rowIndex = $this.data('row-index') || $this.closest('tr').index();
                    var $row = $this.closest('tr');
                    var inmateIdPrefix = $row.find('select[id*="InmateIdPrefix"], select[name*="InmateIdPrefix"]').val() || '';

                    console.log('Keydown event - rowIndex:', rowIndex, 'inmateIdPrefix:', inmateIdPrefix);
                    autoPopulateOffender(this, rowIndex, inmateIdPrefix);
                    return false;
                }
            });

            // Add blur event handler for delete eligibility check
            $(document).on('blur', '.auto-populate-field', function () {
                var $this = $(this);
                var $row = $this.closest('tr');
                var inmateIdPrefix = $row.find('select[id*="InmateIdPrefix"], select[name*="InmateIdPrefix"]').val() || '';
                var offenderId = $this.val().trim();

                if (offenderId && inmateIdPrefix) {
                    console.log('Blur event - checking delete eligibility for:', inmateIdPrefix + offenderId);
                    checkDeleteEligibility(inmateIdPrefix, offenderId);
                } else {
                    // If no offender ID, check if any other rows have offender IDs
                    var hasAnyOffenderId = false;
                    $('#inmateTable tbody tr').each(function() {
                        var rowOffenderId = $(this).find('input[id*="OffenderId"], input[name*="OffenderId"]').val();
                        if (rowOffenderId && rowOffenderId.trim() !== '') {
                            hasAnyOffenderId = true;
                            return false; // break out of each loop
                        }
                    });

                    if (!hasAnyOffenderId) {
                        // No offender IDs in any row, reset Mant control and use default button state
                        deleteButtonControlledByMant = false;
                        updateDeleteButtonState(false);
                    }
                }
            });

            // Function to clear offender fields
            function clearOffenderFields(rowIndex) {
                var $row = $('#inmateTable tbody tr').eq(rowIndex);
                $row.find('input[id*="LastName"], input[name*="LastName"]').val('');
                $row.find('input[id*="FirstName"], input[name*="FirstName"]').val('');
                $row.find('input[id*="frominsText"], input[name*="frominsText"]').val('');
                $row.find('input[id*="Instno"], input[name*="Instno"]').val('');
                //$row.find('select[id*="InmateIdPrefix"], select[name*="InmateIdPrefix"]').val('');
                $row.find('input[id*="OffenderId"], input[name*="OffenderId"]').val('');

                // When clearing fields, reset Mant control flag and disable delete button
                deleteButtonControlledByMant = false;
                updateDeleteButtonState(false);
            }



            // Function to clear the table except for one empty row
            MOSCI.clearTable = function() {
                $('#inmateTable tbody tr').not(':first').remove();
                var $firstRow = $('#inmateTable tbody tr:first');
                $firstRow.find('input[type="text"]').val('');
                $firstRow.find('textarea').val('');
                $firstRow.find('select').prop('selectedIndex', 0);
                $firstRow.find('input[type="checkbox"]').prop('checked', false);
                $firstRow.find('input[type="hidden"]').val('');
            };
            // Function to update row indices and field names after adding/removing rows
            // FIXED: Calendar issue after row removal - This function now properly destroys and reinitializes
            // datepicker instances after updating row indices to prevent calendar opening in wrong row
            MOSCI.updateRowIndices = function () {
                console.log('=== Starting updateRowIndices ===');

                // First, destroy all existing datepicker instances to prevent orphaned references
                $('.datepicker-input').each(function () {
                    var $input = $(this);
                    if ($input.hasClass('hasDatepicker')) {
                        try {
                            $input.datepicker('destroy');
                            console.log('Destroyed datepicker for input:', $input.attr('id'));
                        } catch (e) {
                            console.log('Error destroying datepicker:', e);
                        }
                    }
                    // Clean up any residual datepicker data
                    $input.removeClass('hasDatepicker ui-datepicker-input');
                    $input.removeData('datepicker');
                    $input.removeAttr('data-datepicker');
                    $input.removeAttr('aria-describedby');
                });

                $('#inmateTable tbody tr').each(function (index) {
                    var $row = $(this);

                    // Update all input field names and IDs to use the correct index
                    $row.find('input, select').each(function () {
                        var $field = $(this);
                        var name = $field.attr('name');
                        var id = $field.attr('id');

                        if (name && name.includes('Inmates[')) {
                            // Update the index in the name attribute
                            var newName = name.replace(/Inmates\[\d+\]/, 'Inmates[' + index + ']');
                            $field.attr('name', newName);
                        }

                        if (id && id.includes('Inmates_')) {
                            // Update the index in the id attribute
                            var newId = id.replace(/Inmates_\d+_/, 'Inmates_' + index + '_');
                            $field.attr('id', newId);
                        }
                    });

                    // Update data-row-index attribute for auto-populate fields
                    $row.find('.auto-populate-field').attr('data-row-index', index);
                });

                console.log('Updated row indices for', $('#inmateTable tbody tr').length, 'rows');

                // Reinitialize all datepickers after updating indices
                setTimeout(function () {
                    console.log('=== Reinitializing datepickers after updateRowIndices ===');
                    MOSCI.initializeDatepickers();
                }, 50);
            };

            // Function to reinitialize datepickers for a specific row
            MOSCI.reinitializeRowDatepicker = function ($row) {
                console.log('=== Reinitializing datepicker for specific row ===');

                var $input = $row.find('.datepicker-input');
                if ($input.length > 0) {
                    // Destroy existing datepicker if it exists
                    if ($input.hasClass('hasDatepicker')) {
                        try {
                            $input.datepicker('destroy');
                            console.log('Destroyed existing datepicker for row');
                        } catch (e) {
                            console.log('Error destroying datepicker:', e);
                        }
                    }

                    // Clean up any residual datepicker data
                    $input.removeClass('hasDatepicker ui-datepicker-input');
                    $input.removeData('datepicker');
                    $input.removeAttr('data-datepicker');
                    $input.removeAttr('aria-describedby');

                    // Reinitialize the datepicker
                    setTimeout(function () {
                        try {
                            $input.attr('autocomplete', 'off');
                            $input.attr('readonly', false);

                            $input.datepicker({
                                dateFormat: 'mm/dd/yy',
                                changeMonth: true,
                                changeYear: true,
                                showButtonPanel: false,
                                yearRange: '-10:+10',
                                showOtherMonths: true,
                                selectOtherMonths: false,
                                firstDay: 0,
                                onSelect: function (dateText) {
                                    console.log('Date selected via row reinit:', dateText);
                                    $(this).trigger('change');
                                    updateButtonState();
                                }
                            });

                            if ($input.hasClass('hasDatepicker')) {
                                console.log('Row datepicker reinitialized successfully');
                            } else {
                                console.error('Failed to reinitialize row datepicker');
                            }
                        } catch (error) {
                            console.error('Error reinitializing row datepicker:', error);
                        }
                    }, 10);
                }
            };

            // Enhanced function to initialize datepickers with better reliability
            MOSCI.initializeDatepickers = function () {
                // Check if jQuery UI is loaded
                if (typeof $.fn.datepicker === 'undefined') {
                    console.error('jQuery UI datepicker is not loaded. Please ensure jQuery UI is included.');
                    return;
                }

                // Initialize datepickers for all date inputs that don't already have it
                $('.datepicker-input').each(function () {
                    var $input = $(this);
                    if (!$input.hasClass('hasDatepicker')) {
                        console.log('Initializing datepicker for input:', $input[0]);

                        // Clean up any residual data first
                        $input.removeClass('hasDatepicker ui-datepicker-input');
                        $input.removeData('datepicker');
                        $input.removeAttr('data-datepicker');
                        $input.removeAttr('aria-describedby');

                        try {
                            // Ensure input is properly prepared
                            $input.attr('autocomplete', 'off');
                            $input.attr('readonly', false);

                            $input.datepicker({
                                dateFormat: 'mm/dd/yy',
                                changeMonth: true,
                                changeYear: true,
                                showButtonPanel: false,
                                yearRange: '-10:+10',
                                showOtherMonths: true,
                                selectOtherMonths: false,
                                firstDay: 0, // Sunday
                                onSelect: function (dateText) {
                                    console.log('Date selected via datepicker:', dateText);
                                    $(this).trigger('change');
                                    // Update button states when schedule date changes
                                    updateButtonState();
                                }
                            });

                            // Verify initialization was successful
                            if ($input.hasClass('hasDatepicker')) {
                                console.log('Datepicker initialized successfully for input');

                                // Add enhanced click handlers to input
                                $input.off('focus.datepicker click.datepicker').on('focus.datepicker click.datepicker', function () {
                                    console.log('Input focused/clicked, showing datepicker');
                                    var $this = $(this);
                                    setTimeout(function() {
                                        try {
                                            if (typeof $this.datepicker === 'function' && $this.hasClass('hasDatepicker')) {
                                                $this.datepicker('show');
                                            }
                                        } catch (error) {
                                            console.error('Error showing datepicker on focus/click:', error);
                                        }
                                    }, 10);
                                });
                            } else {
                                console.error('Datepicker initialization failed for input:', $input[0]);
                            }
                        } catch (error) {
                            console.error('Error initializing datepicker:', error);
                        }
                    }
                });
            };

            // Handle calendar icon clicks with event delegation - simplified approach
            $(document).on('click', '.datepicker-trigger', function (e) {
                e.preventDefault();
                e.stopPropagation();

                var $trigger = $(this);
                var $input = $trigger.siblings('.datepicker-input');
                // If no sibling found, try to find within the same input-group
                if ($input.length === 0) {
                    $input = $trigger.closest('.input-group').find('.datepicker-input');
                }

                // If still not found, try to find within the same table cell
                if ($input.length === 0) {
                    $input = $trigger.closest('td').find('.datepicker-input');
                }

                console.log('=== Calendar Icon Clicked ===');
                console.log('Trigger element:', $trigger[0]);
                console.log('Input found:', $input.length);
                console.log('Input element:', $input[0]);

                var $row = $trigger.closest('tr');
                var rowIndex = $row.index();
                var inputId = $input.length > 0 ? $input.attr('id') : 'none';
                var inputName = $input.length > 0 ? $input.attr('name') : 'none';

                console.log('Row index:', rowIndex);
                console.log('Input ID:', inputId);
                console.log('Input name:', inputName);
                console.log('Total rows in table:', $('#inmateTable tbody tr').length);

                if ($input.length > 0) {
                    console.log('Input has datepicker class:', $input.hasClass('hasDatepicker'));

                    // Enhanced force initialization for datepicker if not already done
                    if (!$input.hasClass('hasDatepicker')) {
                        console.log('Force initializing datepicker for calendar icon click...');

                        // Check if jQuery UI is available
                        if (typeof $.fn.datepicker === 'undefined') {
                            console.error('jQuery UI datepicker is not available');
                            $input.focus();
                            return;
                        }

                        // Clean up any residual data first
                        $input.removeClass('hasDatepicker ui-datepicker-input');
                        $input.removeData('datepicker');
                        $input.removeAttr('data-datepicker');
                        $input.removeAttr('aria-describedby');

                        // Use enhanced initialization attempts for better reliability
                        var initAttempts = 0;
                        var maxAttempts = 5;

                        function attemptInitialization() {
                            initAttempts++;
                            try {
                                console.log('Calendar icon initialization attempt:', initAttempts);

                                // Ensure input is properly prepared
                                $input.attr('autocomplete', 'off');
                                $input.attr('readonly', false);

                                $input.datepicker({
                                    dateFormat: 'mm/dd/yy',
                                    changeMonth: true,
                                    changeYear: true,
                                    showButtonPanel: false,
                                    yearRange: '-10:+10',
                                    showOtherMonths: true,
                                    selectOtherMonths: false,
                                    firstDay: 0,
                                    onSelect: function (dateText) {
                                        console.log('Date selected via calendar icon:', dateText);
                                        $(this).trigger('change');
                                        // Update button states when schedule date changes
                                        updateButtonState();
                                    }
                                });

                                if ($input.hasClass('hasDatepicker')) {
                                    console.log('Calendar icon datepicker initialized successfully (attempt ' + initAttempts + ')');

                                    // Try to show the datepicker immediately
                                    setTimeout(function() {
                                        try {
                                            $input.datepicker('show');
                                            console.log('Datepicker shown after initialization');
                                        } catch (showError) {
                                            console.error('Error showing datepicker after initialization:', showError);
                                        }
                                    }, 10);

                                    return true;
                                } else if (initAttempts < maxAttempts) {
                                    console.log('Calendar icon datepicker not ready, retrying in 100ms...');
                                    setTimeout(attemptInitialization, 100);
                                    return false;
                                } else {
                                    console.error('Failed to initialize calendar icon datepicker after ' + maxAttempts + ' attempts');
                                    $input.focus();
                                    return false;
                                }
                            } catch (initError) {
                                console.error('Error initializing calendar icon datepicker (attempt ' + initAttempts + '):', initError);
                                if (initAttempts < maxAttempts) {
                                    setTimeout(attemptInitialization, 100);
                                    return false;
                                } else {
                                    $input.focus();
                                    return false;
                                }
                            }
                        }

                        if (!attemptInitialization()) {
                            return;
                        }
                    }

                    // Enhanced datepicker show logic with better error handling
                    setTimeout(function() {
                        try {
                            console.log('Attempting to show datepicker...');
                            // Check if datepicker is actually available and initialized
                            if (typeof $input.datepicker === 'function' && $input.hasClass('hasDatepicker')) {
                                $input.datepicker('show');
                                console.log('Datepicker show command executed successfully');
                            } else {
                                console.warn('Datepicker not properly initialized, attempting final initialization...');

                                // Final attempt to initialize before giving up
                                try {
                                    // Clean up first
                                    $input.removeClass('hasDatepicker ui-datepicker-input');
                                    $input.removeData('datepicker');
                                    $input.removeAttr('data-datepicker');
                                    $input.removeAttr('aria-describedby');

                                    // Prepare input
                                    $input.attr('autocomplete', 'off');
                                    $input.attr('readonly', false);

                                    $input.datepicker({
                                        dateFormat: 'mm/dd/yy',
                                        changeMonth: true,
                                        changeYear: true,
                                        showButtonPanel: false,
                                        yearRange: '-10:+10',
                                        showOtherMonths: true,
                                        selectOtherMonths: false,
                                        firstDay: 0,
                                        onSelect: function (dateText) {
                                            console.log('Date selected via final attempt:', dateText);
                                            $(this).trigger('change');
                                            // Update button states when schedule date changes
                                            updateButtonState();
                                        }
                                    });

                                    // Try to show it immediately after final initialization
                                    if ($input.hasClass('hasDatepicker')) {
                                        setTimeout(function() {
                                            $input.datepicker('show');
                                            console.log('Datepicker initialized and shown successfully on final attempt');
                                        }, 50);
                                    } else {
                                        console.error('Final datepicker initialization failed, focusing input');
                                        $input.focus();
                                    }
                                } catch (retryError) {
                                    console.error('Final initialization attempt failed:', retryError);
                                    $input.focus();
                                }
                            }
                        } catch (showError) {
                            console.error('Error showing datepicker:', showError);
                            // Fallback: focus the input
                            console.log('Fallback: focusing input');
                            $input.focus();
                        }
                    }, 10);
                } else {
                    console.error('No input sibling found for calendar trigger');
                    console.log('Trigger parent:', $trigger.parent()[0]);
                    console.log('All inputs in parent:', $trigger.parent().find('input').length);
                }
            });



            $(function () {

               

                // Debug form submission
                $('#Mosci').on('submit', function (e) {
                    console.log('Form submission detected');
                    var formData = $(this).serialize();
                    console.log('Form data:', formData);

                    // Check if this is a search submission - look for the clicked button
                    var submitAction = $(document.activeElement).val() || $('button[type="submit"]:focus').val();
                    console.log('Submit action:', submitAction);

                    // Log search-specific values
                    var searchPrefix = $('#searchPrefixDropdown').val();
                    var searchOffenderId = $('#txtInmateNum').val();
                    if (searchOffenderId != '' && searchPrefix != '') {

                        $('#btnAddNewInmate').prop('disabled', true);
                    }
                    console.log('Search values - Prefix:', searchPrefix, 'OffenderId:', searchOffenderId);

                    if (submitAction === 'Search') {
                        console.log('Search form submission detected');
                        console.log('Final search values being submitted - Prefix:', searchPrefix, 'OffenderId:', searchOffenderId);

                    } else if (submitAction === 'Save') {
                        console.log('=== SAVE FORM SUBMISSION DETECTED ===');

                        // Log all table rows and their data
                        var rowCount = $('#inmateTable tbody tr').length;
                        console.log('Total rows in table:', rowCount);

                        $('#inmateTable tbody tr').each(function (index) {
                            var $row = $(this);
                            var rowData = {
                                index: index,
                                //recno: $row.find('input[id*="Recno"]').val(),
                                prefix: $row.find('select[id*="InmateIdPrefix"]').val(),
                                offenderId: $row.find('input[id*="OffenderId"]').val(),
                                //combinedOffenderId: $row.find('input[id*="CombinedOffenderId"]').val(),
                                lastName: $row.find('input[id*="LastName"]').val(),
                                firstName: $row.find('input[id*="FirstName"]').val(),
                                fromInstitutionId: $row.find('input[id*="frominsText"]').val(),
                                //toInstitutionId: $row.find('select[id*="ToInstitutionId"]').val(),
                                schDate: $row.find('input[id*="SchDate"]').val(),
                                descrl: $row.find('textarea[id*="Descrl"]').val(),
                                isMarkedForRemoval: $row.find('input[id*="IsMarkedForRemoval"]:checked').length > 0,
                                isMarkedForDeletion: $row.find('input[id*="IsMarkedForDeletion"]:checked').length > 0
                            };
                            console.log('Row ' + index + ' data:', rowData);
                        });

                        // Log form field names to verify proper MVC binding
                        console.log('=== Form field names for MVC binding ===');
                        $('#inmateTable tbody tr').each(function (index) {
                            var $row = $(this);
                            $row.find('input, select').each(function () {
                                var $field = $(this);
                                var name = $field.attr('name');
                                var id = $field.attr('id');
                                var value = $field.val();
                                if (name && name.includes('Inmates[')) {
                                    console.log('Field:', name, 'ID:', id, 'Value:', value);
                                }
                            });
                        });
                    }
                });

            // Function to update field names for proper array indexing
            function updateFieldNames($row, index) {
                $row.find('input, select, textarea').each(function() {
                    var $field = $(this);
                    var name = $field.attr('name');
                    var id = $field.attr('id');

                    if (name && name.startsWith('Template')) {
                        // Replace Template prefix with proper array indexing
                        var fieldName = name.replace('Template', '');

                        // Special handling for frominsText field (it doesn't use array indexing)
                        if (fieldName === 'frominsText') {
                            $field.attr('name', 'frominsText');
                            // ID doesn't need array indexing for frominsText
                            if (id && id.startsWith('Template')) {
                                $field.attr('id', 'frominsText');
                            }
                        } else {
                            $field.attr('name', 'Inmates[' + index + '].' + fieldName);
                            // Update ID to match MVC naming convention
                            if (id && id.startsWith('Template')) {
                                var newId = 'Inmates_' + index + '_' + fieldName;
                                // Handle special case for hidden OffenderId field
                                if (id === 'TemplateOffenderId_Hidden') {
                                    newId = 'Inmates_' + index + '_OffenderId_Hidden';
                                }
                                $field.attr('id', newId);
                            }
                        }
                    }
                });
            }

            // Function to initialize all functionality for a new row
            function initializeNewRowFunctionality($newRow) {
                console.log('=== Initializing new row functionality ===');
                console.log('New row element:', $newRow[0]);

                //Initialize datepicker for date inputs with improved reliability
                if (typeof $.fn.datepicker !== 'undefined') {
                    $newRow.find('.datepicker-input').each(function () {
                        var $input = $(this);
                        console.log('Found datepicker input in new row:', $input[0]);
                        console.log('Input already has datepicker:', $input.hasClass('hasDatepicker'));

                        // Clean up any existing datepicker data completely
                        if ($input.hasClass('hasDatepicker')) {
                            try {
                                $input.datepicker('destroy');
                                console.log('Destroyed existing datepicker for clean initialization');
                            } catch (destroyError) {
                                console.log('Error destroying existing datepicker:', destroyError);
                            }
                        }

                        // Remove all datepicker-related classes and data
                        $input.removeClass('hasDatepicker ui-datepicker-input');
                        $input.removeData('datepicker');
                        $input.removeAttr('data-datepicker');
                        $input.removeAttr('aria-describedby');

                        // Initialize datepicker with enhanced retry logic and better timing
                        var initAttempts = 0;
                        var maxAttempts = 5;

                        function attemptDatepickerInit() {
                            initAttempts++;
                            try {
                                console.log('Initializing datepicker for new row input (attempt ' + initAttempts + ')...');

                                // Ensure the input is properly prepared
                                $input.attr('autocomplete', 'off');
                                $input.attr('readonly', false);

                                $input.datepicker({
                                    dateFormat: 'mm/dd/yy',
                                    changeMonth: true,
                                    changeYear: true,
                                    showButtonPanel: false,
                                    yearRange: '-10:+10',
                                    showOtherMonths: true,
                                    selectOtherMonths: false,
                                    firstDay: 0, // Sunday
                                    onSelect: function (dateText) {
                                        console.log('Date selected via datepicker in new row:', dateText);
                                        $(this).trigger('change');
                                        // Update button states when schedule date changes
                                        updateButtonState();
                                    }
                                });

                                // Verify initialization was successful
                                if ($input.hasClass('hasDatepicker')) {
                                    console.log('Datepicker initialized successfully for new row (attempt ' + initAttempts + ')');

                                    // Add enhanced click handlers for the input
                                    $input.off('focus.newrow click.newrow').on('focus.newrow click.newrow', function () {
                                        console.log('New row input focused/clicked, showing datepicker');
                                        var $this = $(this);
                                        setTimeout(function() {
                                            try {
                                                if (typeof $this.datepicker === 'function' && $this.hasClass('hasDatepicker')) {
                                                    $this.datepicker('show');
                                                }
                                            } catch (error) {
                                                console.error('Error showing datepicker on focus/click for new row:', error);
                                            }
                                        }, 10);
                                    });

                                    return true; // Success
                                } else if (initAttempts < maxAttempts) {
                                    console.log('Datepicker initialization failed, retrying in 150ms...');
                                    setTimeout(attemptDatepickerInit, 150);
                                    return false;
                                } else {
                                    console.error('Datepicker initialization failed after ' + maxAttempts + ' attempts');
                                    return false;
                                }
                            } catch (error) {
                                console.error('Error initializing datepicker for new row (attempt ' + initAttempts + '):', error);
                                if (initAttempts < maxAttempts) {
                                    setTimeout(attemptDatepickerInit, 150);
                                    return false;
                                } else {
                                    return false;
                                }
                            }
                        }

                        attemptDatepickerInit();
                    });
                } else {
                    console.warn('jQuery UI datepicker not available for new row initialization');
                }

                // Initialize numeric validation for offender ID fields
                $newRow.find('.onlyNumeric').off('keypress.numeric').on('keypress.numeric', function(e) {
                    // Allow only numbers (0-9)
                    if (e.which < 48 || e.which > 57) {
                        e.preventDefault();
                    }
                });

                

                //  Initialize datepicker trigger functionality - this is critical for new rows
                $newRow.find('.datepicker-trigger').off('click.newrowdatepicker').on('click.newrowdatepicker', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    var $trigger = $(this);
                    var $input = $trigger.siblings('.datepicker-input');

                    console.log('=== New Row Calendar Icon Clicked ===');
                    console.log('Trigger element:', $trigger[0]);
                    console.log('Input found:', $input.length);
                    console.log('Input element:', $input[0]);

                    if ($input.length > 0) {
                        console.log('Input has datepicker class:', $input.hasClass('hasDatepicker'));

                        // Force initialize datepicker if not already done
                        if (!$input.hasClass('hasDatepicker')) {
                            console.log('Force initializing datepicker for new row...');

                            if (typeof $.fn.datepicker === 'undefined') {
                                console.error('jQuery UI datepicker is not available');
                                $input.focus();
                                return;
                            }

                            try {
                                $input.datepicker({
                                    dateFormat: 'mm/dd/yy',
                                    changeMonth: true,
                                    changeYear: true,
                                    showButtonPanel: false,
                                    yearRange: '-10:+10',
                                    showOtherMonths: true,
                                    selectOtherMonths: false,
                                    firstDay: 0,
                                    onSelect: function (dateText) {
                                        console.log('Date selected:', dateText);
                                        $(this).trigger('change');
                                        // Update button states when schedule date changes
                                        updateButtonState();
                                    }
                                });
                                console.log('Datepicker initialized successfully for new row');
                            } catch (initError) {
                                console.error('Error initializing datepicker for new row:', initError);
                                $input.focus();
                                return;
                            }
                        }

                        // Try to show the datepicker with multiple attempts
                        var showAttempts = 0;
                        var maxAttempts = 3;

                        function attemptShow() {
                            showAttempts++;
                            try {
                                console.log('Attempting to show datepicker for new row (attempt ' + showAttempts + ')...');
                                if (typeof $input.datepicker === 'function' && $input.hasClass('hasDatepicker')) {
                                    $input.datepicker('show');
                                    console.log('Datepicker show command executed for new row');
                                } else if (showAttempts < maxAttempts) {
                                    console.warn('Datepicker not ready, retrying in 50ms...');
                                    setTimeout(attemptShow, 50);
                                } else {
                                    console.warn('Datepicker not properly initialized for new row after ' + maxAttempts + ' attempts, focusing input instead');
                                    $input.focus();
                                }
                            } catch (showError) {
                                console.error('Error showing datepicker for new row (attempt ' + showAttempts + '):', showError);
                                if (showAttempts < maxAttempts) {
                                    setTimeout(attemptShow, 50);
                                } else {
                                    console.log('Fallback: focusing input for new row');
                                    $input.focus();
                                }
                            }
                        }

                        attemptShow();
                    } else {
                        console.error('No input sibling found for calendar trigger in new row');
                    }
                });

                // 5. Update row index data attribute for auto-populate
                var rowIndex = $newRow.index();
                $newRow.find('.auto-populate-field').attr('data-row-index', rowIndex);

                console.log('Initialized functionality for new row at index:', rowIndex);
            }

            // Clone first row and clear inputs when adding a new inmate
            $('#btnAddNewInmate').on('click', function (e) {
                e.preventDefault();

                // Check if we've reached the maximum number of rows
                var currentRowCount = $('#inmateTable tbody tr').length;
                if (currentRowCount >= MOSCI.MAX_ROWS) {
                    alert('Maximum number of rows (' + MOSCI.MAX_ROWS + ') reached. Cannot add more rows.');
                    return null;
                }

                var $newRow;

                // Check if there are existing rows to clone from
                if (currentRowCount > 0) {
                    // Clone the template row or first row
                    var $templateRow = $('#inmate-row-template');
                    if ($templateRow.length === 0) {
                        $templateRow = $('#inmateTable tbody tr:first');
                    }
                    $newRow = $templateRow.clone();

                    // Generate a unique ID for the new row
                    var rowId = 'inmate-row-' + new Date().getTime();
                    $newRow.attr('id', rowId);

                    // Clear all input values
                    $newRow.find('input[type="text"]').val('');
                    $newRow.find('textarea').val('');
                    $newRow.find('select').prop('selectedIndex', 0);
                    $newRow.find('input[type="checkbox"]').prop('checked', false);

                    // Clean up any existing datepicker data from cloned row
                    $newRow.find('.datepicker-input').each(function() {
                        var $input = $(this);
                        // Remove any datepicker classes and data from the cloned element
                        $input.removeClass('hasDatepicker');
                        $input.removeData('datepicker');
                        $input.removeAttr('data-datepicker');
                        // Remove any jQuery UI specific attributes
                        $input.removeAttr('aria-describedby');
                        console.log('Cleaned datepicker data from cloned row input:', $input[0]);
                    });

                    // Update field names for proper array indexing
                    $newRow.find('input, select, textarea').each(function() {
                        var $field = $(this);
                        var name = $field.attr('name');
                        if (name && name.includes('Inmates[')) {
                            // Update the index in the field name
                            var newName = name.replace(/Inmates\[\d+\]/, 'Inmates[' + currentRowCount + ']');
                            $field.attr('name', newName);
                        }
                    });
                } else {
                    // Use the hidden template when no rows exist
                    console.log('=== Creating row from empty template ===');
                    console.log('Template element:', $('#empty-row-template')[0]);

                    $newRow = $('#empty-row-template').clone();

                    // Generate a unique ID for the new row
                    var rowId = 'inmate-row-' + new Date().getTime();
                    $newRow.attr('id', rowId);

                    console.log('Cloned template row:', $newRow[0]);
                    console.log('Template row datepicker inputs:', $newRow.find('.datepicker-input').length);
                    console.log('Template row datepicker triggers:', $newRow.find('.datepicker-trigger').length);

                    // Update the field names to use proper array indexing
                    updateFieldNames($newRow, currentRowCount);

                    // Ensure the cloned elements have the correct classes and attributes
                    $newRow.find('.datepicker-input').each(function() {
                        var $input = $(this);
                        // Make sure the input has the correct classes
                        if (!$input.hasClass('datepicker-input')) {
                            $input.addClass('datepicker-input');
                        }
                        // Remove any existing datepicker classes from the template
                        $input.removeClass('hasDatepicker');
                        console.log('Prepared datepicker input for template row:', $input[0]);
                    });

                    console.log('Updated field names and prepared datepicker elements for template row');
                }

                // Append the new row to the table
                $('#inmateTable tbody').append($newRow);

                // Use enhanced timing to ensure DOM is fully updated before initializing functionality
                setTimeout(function() {
                    console.log('=== Starting new row initialization ===');
                    console.log('New row count:', $('#inmateTable tbody tr').length);

                    // Initialize all functionality for the new row
                    initializeNewRowFunctionality($newRow);

                    // Update row indices for all rows
                    MOSCI.updateRowIndices();

                    // Additional datepicker initialization with enhanced timing
                    setTimeout(function() {
                        console.log('=== Secondary datepicker initialization for new row ===');

                        // Use the specialized template datepicker function for all new rows
                        MOSCI.ensureTemplateDatepicker($newRow);

                        // Also run the general datepicker initialization to catch any missed inputs
                        MOSCI.initializeDatepickers();

                        // Final verification
                        setTimeout(function() {
                            var $newRowInput = $newRow.find('.datepicker-input');
                            console.log('Final verification - New row datepicker initialized:', $newRowInput.hasClass('hasDatepicker'));
                            if (!$newRowInput.hasClass('hasDatepicker')) {
                                console.warn('Datepicker not initialized, attempting final initialization...');
                                MOSCI.ensureTemplateDatepicker($newRow);
                            }
                        }, 100);

                    }, 50);

                    console.log('New row added and initialization started');
                }, 20);

                //update button state after adding new rows
                updateButtonState();

                // Scroll to the new row
                $('html, body').animate({
                    scrollTop: $newRow.offset().top - 100
                }, 500);
            });

            // Enhanced function to ensure datepicker works for template-based rows
            MOSCI.ensureTemplateDatepicker = function($row) {
                console.log('=== Ensuring datepicker for template-based row ===');

                var $input = $row.find('.datepicker-input');
                var $trigger = $row.find('.datepicker-trigger');

                console.log('Found datepicker input:', $input.length);
                console.log('Found datepicker trigger:', $trigger.length);

                if ($input.length > 0) {
                    // Completely clean up any existing datepicker
                    if ($input.hasClass('hasDatepicker')) {
                        try {
                            $input.datepicker('destroy');
                            console.log('Destroyed existing template datepicker');
                        } catch (e) {
                            console.log('Error destroying existing datepicker:', e);
                        }
                    }

                    // Remove all datepicker-related classes and data
                    $input.removeClass('hasDatepicker ui-datepicker-input');
                    $input.removeData('datepicker');
                    $input.removeAttr('data-datepicker');
                    $input.removeAttr('aria-describedby');

                    // Enhanced initialization with multiple retry attempts
                    var initAttempts = 0;
                    var maxAttempts = 5;

                    function attemptTemplateInit() {
                        initAttempts++;
                        console.log('Template datepicker initialization attempt:', initAttempts);

                        try {
                            // Ensure input is properly prepared
                            $input.attr('autocomplete', 'off');
                            $input.attr('readonly', false);

                            $input.datepicker({
                                dateFormat: 'mm/dd/yy',
                                changeMonth: true,
                                changeYear: true,
                                showButtonPanel: false,
                                yearRange: '-10:+10',
                                showOtherMonths: true,
                                selectOtherMonths: false,
                                firstDay: 0,
                                onSelect: function (dateText) {
                                    console.log('Date selected via template datepicker:', dateText);
                                    $(this).trigger('change');
                                    // Update button states when schedule date changes
                                    updateButtonState();
                                }
                            });

                            if ($input.hasClass('hasDatepicker')) {
                                console.log('Template datepicker initialized successfully (attempt ' + initAttempts + ')');

                                // Add enhanced event handlers for template row
                                $input.off('focus.template click.template').on('focus.template click.template', function () {
                                    console.log('Template row input focused/clicked, showing datepicker');
                                    var $this = $(this);
                                    setTimeout(function() {
                                        try {
                                            if (typeof $this.datepicker === 'function' && $this.hasClass('hasDatepicker')) {
                                                $this.datepicker('show');
                                            }
                                        } catch (error) {
                                            console.error('Error showing template datepicker on focus/click:', error);
                                        }
                                    }, 10);
                                });

                                return true; // Success
                            } else if (initAttempts < maxAttempts) {
                                console.log('Template datepicker initialization failed, retrying in 200ms...');
                                setTimeout(attemptTemplateInit, 200);
                                return false;
                            } else {
                                console.error('Template datepicker initialization failed after ' + maxAttempts + ' attempts');
                                return false;
                            }
                        } catch (error) {
                            console.error('Error initializing template datepicker (attempt ' + initAttempts + '):', error);
                            if (initAttempts < maxAttempts) {
                                setTimeout(attemptTemplateInit, 200);
                                return false;
                            } else {
                                return false;
                            }
                        }
                    }

                    // Start the initialization process with a small delay
                    setTimeout(attemptTemplateInit, 100);
                }
            };

            // Wait a bit for jQuery UI to be fully loaded
            setTimeout(function () {
                console.log('=== Initializing Datepickers ===');
                console.log('jQuery UI datepicker available:', typeof $.fn.datepicker !== 'undefined');
                console.log('Found datepicker inputs:', $('.datepicker-input').length);

                // Initialize datepickers for existing rows
                MOSCI.initializeDatepickers();

                // Verify initialization
                setTimeout(function () {
                    console.log('Datepicker inputs with hasDatepicker class:', $('.datepicker-input.hasDatepicker').length);

                    // Test direct click on first input
                    var $firstInput = $('.datepicker-input').first();
                    if ($firstInput.length > 0) {
                        console.log('First input element:', $firstInput[0]);
                        console.log('First input has datepicker:', $firstInput.hasClass('hasDatepicker'));
                        $('#txtInmateNum').val('');
                        $('#searchPrefixDropdown').val('A');
                    }

                    // If jQuery UI is still not loaded, try again
                    if (typeof $.fn.datepicker === 'undefined') {
                        console.warn('jQuery UI still not loaded, trying again in 500ms...');
                        setTimeout(function() {
                            console.log('=== Retry Initializing Datepickers ===');
                            MOSCI.initializeDatepickers();
                        }, 500);
                    }
                }, 200);
            }, 100);

            // Special handling for empty grid scenario
            setTimeout(function() {
                var currentRowCount = $('#inmateTable tbody tr').length;
                console.log('=== Checking grid state ===');
                console.log('Current row count:', currentRowCount);

                if (currentRowCount === 0) {
                    console.log('Grid is empty - ensuring Add New Inmate button is ready');
                    
                    $('#btnAddNewInmate').prop('disabled', true).attr('title', 'Add new inmate is unavailable');
                }
            }, 300);

           

                // Function to perform the actual removal (called after user confirms in modal)
                function performRemoval() {
                    var hasChecked = false;

                    // Remove the checked rows
                    $('#inmateTable tbody tr').each(function () {
                        var $row = $(this);
                        if ($row.find('input[id*="IsMarkedForRemoval"]').is(':checked')) {
                            $row.remove();
                            hasChecked = true;
                        }
                    });

                    if (hasChecked) {
                        console.log('=== Performing row removal cleanup ===');

                        // Update row indices (this will also reinitialize datepickers)
                        if (typeof MOSCI !== 'undefined' && typeof MOSCI.updateRowIndices === 'function') {
                            MOSCI.updateRowIndices();
                        }

                        // Additional step: ensure all remaining rows have working datepickers
                        setTimeout(function () {
                            console.log('=== Additional datepicker verification after removal ===');
                            $('#inmateTable tbody tr').each(function () {
                                var $row = $(this);
                                var $input = $row.find('.datepicker-input');
                                if ($input.length > 0 && !$input.hasClass('hasDatepicker')) {
                                    console.log('Found row without datepicker, reinitializing...');
                                    if (typeof MOSCI !== 'undefined' && typeof MOSCI.reinitializeRowDatepicker === 'function') {
                                        MOSCI.reinitializeRowDatepicker($row);
                                    }
                                }
                            });
                        }, 100);

                        alert('Selected inmate(s) removed successfully.');
                    }

                    //updateButtonState();
                    if (typeof updateButtonState === 'function') {
                        updateButtonState();
                    } else {
                        console.warn('updateButtonState function not available after removal');
                    }
                }


                // Handle remove inmate button - using submitAction value instead of name
                $('button[value="RemoveSelected"]').on('click', function (e) {
                    e.preventDefault();

                    // Perform initial validation before showing modal
                    var checkedCount = 0;
                    var totalRows = $('#inmateTable tbody tr').length;

                    // Count checked rows first
                    $('#inmateTable tbody tr').each(function () {
                        var $row = $(this);
                        if ($row.find('input[id*="IsMarkedForRemoval"]').is(':checked')) {
                            checkedCount++;
                        }
                    });

                    if (checkedCount === 0) {
                        alert('Please select at least one inmate to remove.');
                        return;
                    }

                    // Check if removing would leave at least one row
                    if (totalRows - checkedCount < 1) {
                        alert('Cannot remove all rows. At least one row must remain in the table.');
                        return;
                    }

                    // Show custom confirmation modal instead of confirm popup
                    // Original code: if (!confirm("Are you sure you want to remove selected inmates")) { return; }
                    $('#removeConfirmModal').modal('show');
                });

                // Handle Yes button in remove confirmation modal
                $('#confirmRemoveBtn').on('click', function () {
                    $('#removeConfirmModal').modal('hide');
                    performRemoval();
                });

                // Function to perform the actual deletion (called after user confirms in modal)
                function performDeletion()
                {

                var checkedRows = [];
                //var totalRows = $('#inmateTable tbody tr').length;

                // Collect all checked rows with their data
                $('#inmateTable tbody tr').each(function() {
                    var $row = $(this);
                    if ($row.find('input[id*="IsMarkedForDeletion"]').is(':checked')) {
                        var prefix = $row.find('select[id*="InmateIdPrefix"]').val();
                        var offenderId = $row.find('input[id*="OffenderId"]').val();
                        var schduleDate = $row.find('input[id*="SchDate"]').val();

                        // Only include rows with valid data
                        if (prefix && offenderId && offenderId.trim() !== '' && schduleDate !== '') {
                            checkedRows.push({
                                row: $row,
                                prefix: prefix,
                                offenderId: offenderId.trim(),
                                schduleDate: schduleDate
                            });
                        }
                    }
                });

               

                //  proceed with deletion
                var deletionPromises = [];
                var successCount = 0;
                var errorCount = 0;
                var errorMessages = [];

               // Process each checked row
                checkedRows.forEach(function(rowData) {
                    // Create the data structure expected by MosciPageViewModel
                    var viewModelData = {
                        'Inmates[0].InmateIdPrefix': rowData.prefix,
                        'Inmates[0].OffenderId': rowData.offenderId,
                        'Inmates[0].IsMarkedForDeletion': true,
                        'Inmates[0].SchDate': rowData.schduleDate
                    };

                    var promise = $.ajax({
                        url: '@Url.Action("DeleteMosciRecord", "Mosci", new { area = "Transfers" })',
                        type: 'POST',
                        data: viewModelData
                    }).done(function(response) {
                        if (response.success) {
                            // Clear the row (make it blank but keep the structure)
                            rowData.row.find('input[type="text"]').val('');
                            rowData.row.find('textarea').val('');
                            rowData.row.find('select').prop('selectedIndex', 0);
                            rowData.row.find('input[type="checkbox"]').prop('checked', false);
                            rowData.row.find('input[type="hidden"]').val('');
                            successCount++;
                        } else {
                            errorCount++;
                            errorMessages.push(response.message || 'Unknown error occurred');
                        }
                    }).fail(function(xhr, status, error) {
                        errorCount++;
                        errorMessages.push('Network error: ' + error);
                    });

                    deletionPromises.push(promise);
                });

                // Wait for all deletion requests to complete
                $.when.apply($, deletionPromises).always(function() {
                    // Show results to user
                    if (successCount > 0 && errorCount === 0) {
                        // All deletions successful
                        console.log('All selected inmates deleted successfully from MOSCI.');
                    } else if (successCount > 0 && errorCount > 0) {
                        // Some successful, some failed
                        alert('Some deletions completed successfully, but ' + errorCount + ' failed:\n' + errorMessages.join('\n'));
                    } else if (errorCount > 0) {
                        // All failed
                        alert('Failed to delete inmates:\n' + errorMessages.join('\n'));
                    }

                    updateButtonState();
                });
            }

            // Handle delete button - using AJAX calls for individual record deletion
            $('button[value="DeleteSelected"]').on('click', function(e) {
                e.preventDefault();

                // Check if button is disabled
                if ($(this).prop('disabled')) {
                    return;
                }

                // Perform initial validation before showing modal
                var checkedRows = [];
                var totalRows = $('#inmateTable tbody tr').length;

                // Collect all checked rows with their data
                $('#inmateTable tbody tr').each(function() {
                    var $row = $(this);
                    if ($row.find('input[id*="IsMarkedForDeletion"]').is(':checked')) {
                        var prefix = $row.find('select[id*="InmateIdPrefix"]').val();
                        var offenderId = $row.find('input[id*="OffenderId"]').val();
                        var schduleDate = $row.find('input[id*="SchDate"]').val();

                        // Only include rows with valid data
                        if (prefix && offenderId && offenderId.trim() !== '' && schduleDate !== '') {
                            checkedRows.push({
                                row: $row,
                                prefix: prefix,
                                offenderId: offenderId.trim(),
                                schduleDate: schduleDate
                            });
                        }
                    }
                });

                // Validation: Check if any rows are selected
                if (checkedRows.length === 0) {
                    alert('Please select at least one inmate with valid data to delete.');
                    return;
                }

                // Show custom confirmation modal instead of confirm popup
                // Original code: if (!confirm('Are you sure you want to delete an inmate from MOSCI?')) { return; }
                $('#deleteConfirmModal').modal('show');
            });

            // Handle Yes button in delete confirmation modal
            $('#confirmDeleteBtn').on('click', function() {
                $('#deleteConfirmModal').modal('hide');
                performDeletion();
            });


            // Handle cancel button
            $('#btnCancel').on('click', function() {
                // Show custom modal instead of confirm popup
                // Original code: if (confirm('Are you sure you want to cancel? Any unsaved changes will be lost.')) {
                $('#cancelConfirmModal').modal('show');
                window.location.href = '@Url.Action("Index", "InmateInformationCard", new { area = "Transfers" })';
            });

            // Handle Yes button in cancel confirmation modal
            $('#confirmCancelBtn').on('click', function() {
                $('#cancelConfirmModal').modal('hide');
                window.location.href = '@Url.Action("Mosci", "Mosci", new { area = "Transfers" })';
            });


            

            // Add change event handler for schedule date fields to update button states
            $(document).on('change', 'input[id*="SchDate"]', function() {
                console.log('Schedule date changed, updating button states');
                updateButtonState();
            });

            updateButtonState();

            // Initialize numeric validation for existing rows
            $('.onlyNumeric').on('keypress', function(e) {
                // Allow only numbers (0-9)
                if (e.which < 48 || e.which > 57) {
                    e.preventDefault();
                }
            });

            // Add event handler for prefix dropdown changes to update combined offender ID
            $(document).on('change', 'select[id*="InmateIdPrefix"], select[name*="InmateIdPrefix"]', function() {
                var $row = $(this).closest('tr');
                var prefix = $(this).val();
                var offenderId = $row.find('input[id*="OffenderId"], input[name*="OffenderId"]').val();

                var rowIndex = $row.index();

                // Validate for duplicates when prefix changes
                //if (prefix && offenderId && offenderId.trim() !== '') {
                    
                //    var validation = validateDuplicateOffender(rowIndex, prefix, offenderId.trim());
                //    if (!validation.isValid) {
                //        showValidationError(validation.message);
                //        // Reset the prefix to empty to prevent duplicate
                //        $(this).val('');
                //        return;
                //    } else {
                //        hideValidationError();
                //    }
                //}

                var combinedId = prefix && offenderId ? prefix + offenderId : '';
                $row.find('input[id*="CombinedOffenderId"]').val(combinedId);
            });

                // Add event handler for To Institution dropdown changes to validate against From Institution
                $(document).on('change', 'select[id*="SchdInst"]', function () {
                    var $row = $(this).closest('tr');
                    var fromInstitution = $row.find('input[name*="frominsText"]').val();
                    var toInstitutionDropdown = $(this);
                    var toInstitutionText = toInstitutionDropdown.find('option:selected').text();
                    // Check if From and To institutions are the same
                    if (fromInstitution && toInstitutionText && fromInstitution.trim() === toInstitutionText.trim()) {
                        alert('From and To institutions cannot be the same. Please select a different destination institution.');
                        // Reset the dropdown to empty/default selection
                        toInstitutionDropdown.val('');
                        return false;
                    }
                });

            MyScript.init({
                Mosci: '@Url.Action("Mosci", "Mosci", new { area = "Transfers" })',
                    GetEmployeeInfoByOaksId: '@Url.Action("GetEmployeeInfoByOaksId", "Mosci", new { area = "Transfers" })'
                });




            // Enable Enter key to trigger search
            $("#txtInmateNum").keyup(function (event) {
                if (event.keyCode == 13) {
                    $("#btnFindOffender").click();
                }
            });
            
                // Handle save button - ensure all data is properly prepared before submission
                
                $('#btnSave').on('click', function (e) {
                    console.log('=== SAVE BUTTON CLICKED ===');

                    // Prevent default form submission so we can handle data collection
                    e.preventDefault();

                    // Validate that we have at least one row with meaningful data
                    var hasValidData = false;
                    $('#inmateTable tbody tr').each(function () {
                        var $row = $(this);

                        var offenderId = $row.find('input[id*="OffenderId"]').val();
                        

                        //if (offenderId && offenderId.trim() !== '' && lastName && lastName.trim() !== '' && firstName && firstName.trim() !== '' && schDat && schDat.trim() !== '') {
                        if (offenderId && offenderId.trim() !== '') {
                            hasValidData = true;
                            return false; // break out of each loop
                        }
                    });

                    if (!hasValidData) {
                        alert('Please add at least one inmate with an Offender ID and destination institution before saving.');
                        return false;
                    }

                    // Validate that From and To institutions are not the same for any row
                    var hasFromToConflict = false;
                    var conflictRowNumber = 0;
                    $('#inmateTable tbody tr').each(function (index) {
                        var $row = $(this);
                        var offenderId = $row.find('input[id*="OffenderId"]').val();

                        // Only validate rows that have data
                        if (offenderId && offenderId.trim() !== '') {
                            var fromInstitution = $row.find('input[name*="frominsText"]').val();
                            var toInstitutionDropdown = $row.find('select[id*="SchdInst"]');
                            var toInstitutionText = toInstitutionDropdown.find('option:selected').text();

                            if (fromInstitution && toInstitutionText && fromInstitution.trim() === toInstitutionText.trim()) {
                                hasFromToConflict = true;
                                conflictRowNumber = index + 1;
                                return false; // break out of each loop
                            }
                        }
                    });

                    if (hasFromToConflict) {
                        alert('Row ' + conflictRowNumber + ': From and To institutions cannot be the same. Please select a different destination institution.');
                        return false;
                    }

                    console.log('Save validation passed, proceeding with custom data collection');

                    // Collect data from the grid manually to ensure all visible rows are included
                    var gridData = [];
                    $('#inmateTable tbody tr').each(function (index) {
                        var $row = $(this);
                        var offenderId = $row.find('input[id*="OffenderId"]').val();

                        // Only include rows that have meaningful data
                        if (offenderId && offenderId.trim() !== '') {
                            var rowData = {
                                InmateIdPrefix: $row.find('select[id*="InmateIdPrefix"]').val(),
                                OffenderId: offenderId.trim(),
                                LastName: $row.find('input[id*="LastName"]').val(),
                                FirstName: $row.find('input[id*="FirstName"]').val(),
                                Instno: $row.find('input[id*="Instno"]').val(),
                                ToInstitutionId: $row.find('select[id*="SchdInst"]').val(),
                                SchDate: $row.find('input[id*="SchDate"]').val(),
                                Descrl: $row.find('textarea[id*="Descrl"]').val()
                            };
                            gridData.push(rowData);
                        }
                    });

                    console.log('Collected grid data:', gridData);

                    // Submit the form with collected data as JSON
                    var form = $('#Mosci');
                    var formData = form.serialize();

                    // Add the grid data as JSON
                    formData += '&submitAction=Save&modelJson=' + encodeURIComponent(JSON.stringify(gridData));

                    console.log('Final form data to submit:', formData);

                    // Submit using AJAX to ensure proper data handling
                    $.ajax({
                        url: form.attr('action'),
                        type: 'POST',
                        data: formData,
                        success: function (response) {
                            // Replace the entire page content with the response
                            document.open();
                            document.write(response);
                            document.close();
                        },
                        error: function (xhr, status, error) {
                            console.error('Save failed:', error);
                            alert('An error occurred while saving. Please try again.');
                        }
                    });

                    return false;
                });


        });
        </script>


        <script type="text/javascript" src="~/Areas/Rh/Scripts/Common.js"></script>
    }
}